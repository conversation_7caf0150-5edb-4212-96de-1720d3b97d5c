{"name": "react-signature-canvas", "version": "1.0.3", "description": "A React wrapper component around signature_pad. Unopinionated and heavily updated fork of react-signature-pad", "main": "build/index.js", "files": ["src/index.js", "build/"], "author": "<PERSON>", "license": "Apache-2.0", "homepage": "https://github.com/agilgur5/react-signature-canvas", "repository": {"type": "git", "url": "https://github.com/agilgur5/react-signature-canvas.git"}, "bugs": {"url": "https://github.com/agilgur5/react-signature-canvas/issues"}, "keywords": ["react", "react-component", "component", "signature", "sign", "e-sign", "e-signature", "canvas", "trim", "whitespace", "draw", "pad", "wrapper", "signature-pad", "react-signature-pad"], "scripts": {"lint": "standard --parser babel-eslint", "lint:fix": "standard --parser babel-eslint --fix", "start": "webpack-dev-server -d --inline --hot", "dist": "webpack -p --config webpack.production.config.js", "test": "jest", "test:pub": "npm run dist && npm pack", "pub": "npm run dist && npm publish", "changelog": "changelog-maker"}, "peerDependencies": {"prop-types": "^15.5.8", "react": "0.14 - 16", "react-dom": "0.14 - 16"}, "dependencies": {"signature_pad": "^2.3.2", "trim-canvas": "^0.1.0"}, "devDependencies": {"@agilgur5/changelog-maker": "^3.0.0", "babel-core": "^6.26.3", "babel-eslint": "^10.0.2", "babel-jest": "^23.6.0", "babel-loader": "^6.0.0", "babel-preset-es2015": "^6.14.0", "babel-preset-react": "^6.11.1", "babel-preset-stage-2": "^6.13.0", "canvas-prebuilt": "^1.6.11", "css-loader": "^0.24.0", "enzyme": "^3.10.0", "enzyme-adapter-react-16": "^1.14.0", "jest": "^24.8.0", "react": "^16.8.6", "react-dom": "^16.8.6", "react-hot-loader": "^1.2.7", "standard": "^13.0.2", "style-loader": "^0.13.1", "webpack": "^1.12.2", "webpack-dev-server": "^1.10.1"}}