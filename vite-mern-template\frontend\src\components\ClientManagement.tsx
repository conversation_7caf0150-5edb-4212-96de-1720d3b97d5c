import React, { useEffect, useState } from 'react';

interface Client {
  _id?: string;
  internalClientId: string;
  name: string;
  address1: string;
  address2?: string;
  address3?: string;
  city: string;
  state: string;
  zipCode: string;
  contactName: string;
  contactPhone: string;
  contactEmail: string;
  additionalInfo?: string;
  createdAt?: string;
}

const emptyClient: Client = {
  internalClientId: '',
  name: '',
  address1: '',
  address2: '',
  address3: '',
  city: '',
  state: '',
  zipCode: '',
  contactName: '',
  contactPhone: '',
  contactEmail: '',
  additionalInfo: '',
};

// Phone number formatting function
const formatPhoneNumber = (value: string): string => {
  // Remove all non-digits
  const digits = value.replace(/\D/g, '');
  
  // Apply formatting based on length
  if (digits.length <= 3) {
    return digits;
  } else if (digits.length <= 6) {
    return `(${digits.slice(0, 3)}) ${digits.slice(3)}`;
  } else {
    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6, 10)}`;
  }
};

// Extract digits from formatted phone number
const extractPhoneDigits = (formatted: string): string => {
  return formatted.replace(/\D/g, '');
};

const ClientManagement: React.FC = () => {
  const [clients, setClients] = useState<Client[]>([]);
  const [search, setSearch] = useState('');
  const [modalOpen, setModalOpen] = useState(false);
  const [editingClient, setEditingClient] = useState<Client | null>(null);
  const [form, setForm] = useState<Client>(emptyClient);
  const [loading, setLoading] = useState(false);
  const [importing, setImporting] = useState(false);
  const [formErrors, setFormErrors] = useState<any>({});
  const [showAll, setShowAll] = useState(false);

  useEffect(() => {
    fetchClients();
  }, []);

  const fetchClients = async () => {
    setLoading(true);
    try {
      const res = await fetch('/api/clients');
      const data = await res.json();
      // Sort by creation date (newest first) or by name if no creation date
      const sortedData = data.sort((a: Client, b: Client) => {
        if (a.createdAt && b.createdAt) {
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        }
        return a.name.localeCompare(b.name);
      });
      setClients(sortedData);
    } catch (error) {
      console.error('Failed to fetch clients:', error);
    }
    setLoading(false);
  };

  const openAddModal = () => {
    setEditingClient(null);
    setForm(emptyClient);
    setFormErrors({});
    setModalOpen(true);
  };

  const openEditModal = (client: Client) => {
    setEditingClient(client);
    setForm(client);
    setFormErrors({});
    setModalOpen(true);
  };

  const closeModal = () => {
    setModalOpen(false);
    setEditingClient(null);
    setForm(emptyClient);
    setFormErrors({});
  };

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    let { name, value } = e.target;
    
    // State: force 2 uppercase letters
    if (name === 'state') {
      value = value.toUpperCase().replace(/[^A-Z]/g, '').slice(0, 2);
    }
    // Zip: only 5 digits
    if (name === 'zipCode') {
      value = value.replace(/[^0-9]/g, '').slice(0, 5);
    }
    // Phone: format as (*************
    if (name === 'contactPhone') {
      value = formatPhoneNumber(value);
    }
    
    setForm({ ...form, [name]: value });
    setFormErrors({ ...formErrors, [name]: undefined });
  };

  const validateForm = () => {
    const errors: any = {};
    
    if (!/^[A-Z]{2}$/.test(form.state)) {
      errors.state = 'State must be 2-letter abbreviation (e.g., FL)';
    }
    if (!/^\d{5}$/.test(form.zipCode)) {
      errors.zipCode = 'Zip code must be 5 digits';
    }
    
    // Validate phone number (must be exactly 10 digits)
    const phoneDigits = extractPhoneDigits(form.contactPhone);
    if (!/^\d{10}$/.test(phoneDigits)) {
      errors.contactPhone = 'Phone must be 10 digits in format (*************';
    }
    
    // Validate email with more strict pattern
    const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailPattern.test(form.contactEmail)) {
      errors.contactEmail = 'Please enter a valid email address';
    }
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;
    
    setLoading(true);
    try {
      // Store phone number with digits only in database but keep formatted display
      const submitForm = {
        ...form,
        contactPhone: extractPhoneDigits(form.contactPhone)
      };
      
      if (editingClient && editingClient._id) {
        await fetch(`/api/clients/${editingClient._id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(submitForm),
        });
      } else {
        await fetch('/api/clients', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(submitForm),
        });
      }
      closeModal();
      fetchClients();
    } catch (error) {
      console.error('Failed to save client:', error);
    }
    setLoading(false);
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Delete this client?')) return;
    setLoading(true);
    try {
      await fetch(`/api/clients/${id}`, { method: 'DELETE' });
      fetchClients();
    } catch (error) {
      console.error('Failed to delete client:', error);
    }
    setLoading(false);
  };

  // Filtered clients
  const filteredClients = clients.filter(c =>
    c.name.toLowerCase().includes(search.toLowerCase()) ||
    c.internalClientId.toLowerCase().includes(search.toLowerCase())
  );

  // Display clients (show top 5 or all based on showAll state, or filtered results)
  const displayClients = search ? filteredClients : (showAll ? clients : clients.slice(0, 5));

  return (
    <div className="w-full space-y-6">
      <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-100">
        <h2 className="text-2xl font-bold mb-6 border-b pb-2">Client Management</h2>
        
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-6">
          <div className="flex gap-2">
            <input
              type="text"
              placeholder="Search by name or ID..."
              className="border rounded px-3 py-2 focus:ring-2 focus:ring-blue-400"
              value={search}
              onChange={e => setSearch(e.target.value)}
            />
            <button
              className="bg-blue-600 text-white px-4 py-2 rounded font-semibold hover:bg-blue-700 shadow-sm"
              onClick={openAddModal}
            >
              + Add Client
            </button>
          </div>
          <button
            className="bg-green-600 text-white px-4 py-2 rounded font-semibold hover:bg-green-700 shadow-sm"
            onClick={() => setImporting(true)}
          >
            Import from CSV/XLS
          </button>
        </div>

        {/* Show/Hide toggle for non-filtered results */}
        {!search && clients.length > 5 && (
          <div className="mb-4">
            <button
              className="text-blue-600 hover:text-blue-800 text-sm font-semibold"
              onClick={() => setShowAll(!showAll)}
            >
              {showAll ? `Hide (showing all ${clients.length} clients)` : `Show all ${clients.length} clients (currently showing top 5)`}
            </button>
          </div>
        )}

        <div className="w-full overflow-x-auto rounded shadow bg-white border max-h-[60vh] overflow-y-auto">
          <table className="w-full min-w-full text-sm">
            <thead className="bg-gray-50 sticky top-0">
              <tr>
                <th className="p-3 text-left font-semibold text-gray-700">System ID</th>
                <th className="p-3 text-left font-semibold text-gray-700">Internal ID#</th>
                <th className="p-3 text-left font-semibold text-gray-700">Name</th>
                <th className="p-3 text-left font-semibold text-gray-700">Contact</th>
                <th className="p-3 text-left font-semibold text-gray-700">Billing Address</th>
                <th className="p-3 text-left font-semibold text-gray-700">Actions</th>
              </tr>
            </thead>
            <tbody>
              {loading ? (
                <tr><td colSpan={6} className="p-4 text-center">Loading...</td></tr>
              ) : displayClients.length === 0 ? (
                <tr><td colSpan={6} className="p-4 text-center">No clients found.</td></tr>
              ) : displayClients.map(client => (
                <tr key={client._id} className="border-b hover:bg-gray-50">
                  <td className="p-2 font-mono text-xs">{client._id}</td>
                  <td className="p-2">{client.internalClientId}</td>
                  <td className="p-2 font-semibold">{client.name}</td>
                  <td className="p-2">
                    <div>{client.contactName}</div>
                    <div className="text-xs text-gray-500">{formatPhoneNumber(client.contactPhone)}</div>
                    <div className="text-xs text-gray-500">{client.contactEmail}</div>
                  </td>
                  <td className="p-2">
                    <div>{client.address1}</div>
                    {client.address2 && <div>{client.address2}</div>}
                    {client.address3 && <div>{client.address3}</div>}
                    <div className="text-xs text-gray-500">{client.city}, {client.state} {client.zipCode}</div>
                  </td>
                  <td className="p-2 flex gap-2">
                    <button className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs" onClick={() => openEditModal(client)}>Edit</button>
                    <button className="px-2 py-1 bg-red-100 text-red-800 rounded text-xs" onClick={() => handleDelete(client._id!)}>Delete</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add/Edit Modal */}
      {modalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl relative max-h-[95vh] overflow-hidden flex flex-col">
            <div className="flex-shrink-0 p-6 border-b">
              <button className="absolute top-4 right-4 text-gray-500 hover:text-gray-800 text-2xl" onClick={closeModal}>&times;</button>
              <h3 className="text-xl font-bold pr-8">{editingClient ? 'Edit Client' : 'Add Client'}</h3>
            </div>
            <div className="flex-1 overflow-y-auto p-6">
              <form className="grid grid-cols-1 md:grid-cols-2 gap-4" onSubmit={handleSubmit}>
                <div>
                  <label className="block font-semibold mb-1">Internal Client ID#</label>
                  <input name="internalClientId" value={form.internalClientId} onChange={handleFormChange} className="w-full border rounded px-2 py-1" required />
                </div>
                <div>
                  <label className="block font-semibold mb-1">Client Name</label>
                  <input name="name" value={form.name} onChange={handleFormChange} className="w-full border rounded px-2 py-1" required />
                </div>
                <div>
                  <label className="block font-semibold mb-1">Billing Address 1</label>
                  <input name="address1" value={form.address1} onChange={handleFormChange} className="w-full border rounded px-2 py-1" required />
                </div>
                <div>
                  <label className="block font-semibold mb-1">Billing Address 2</label>
                  <input name="address2" value={form.address2} onChange={handleFormChange} className="w-full border rounded px-2 py-1" />
                </div>
                <div>
                  <label className="block font-semibold mb-1">Billing Address 3</label>
                  <input name="address3" value={form.address3} onChange={handleFormChange} className="w-full border rounded px-2 py-1" />
                </div>
                <div>
                  <label className="block font-semibold mb-1">City</label>
                  <input name="city" value={form.city} onChange={handleFormChange} className="w-full border rounded px-2 py-1" required />
                </div>
                <div>
                  <label className="block font-semibold mb-1">State</label>
                  <input name="state" value={form.state} onChange={handleFormChange} className="w-full border rounded px-2 py-1 uppercase" required maxLength={2} placeholder="FL" />
                  {formErrors.state && <div className="text-xs text-red-600 mt-1">{formErrors.state}</div>}
                </div>
                <div>
                  <label className="block font-semibold mb-1">Zip Code</label>
                  <input name="zipCode" value={form.zipCode} onChange={handleFormChange} className="w-full border rounded px-2 py-1" required maxLength={5} placeholder="12345" />
                  {formErrors.zipCode && <div className="text-xs text-red-600 mt-1">{formErrors.zipCode}</div>}
                </div>
                <div>
                  <label className="block font-semibold mb-1">Point of Contact Name</label>
                  <input name="contactName" value={form.contactName} onChange={handleFormChange} className="w-full border rounded px-2 py-1" required />
                </div>
                <div>
                  <label className="block font-semibold mb-1">Contact Phone Number</label>
                  <input
                    name="contactPhone"
                    value={form.contactPhone}
                    onChange={handleFormChange}
                    className="w-full border rounded px-2 py-1"
                    required
                    placeholder="(*************"
                    maxLength={14}
                    inputMode="tel"
                  />
                  {formErrors.contactPhone && <div className="text-xs text-red-600 mt-1">{formErrors.contactPhone}</div>}
                </div>
                <div>
                  <label className="block font-semibold mb-1">Contact Email Address</label>
                  <input
                    name="contactEmail"
                    value={form.contactEmail}
                    onChange={handleFormChange}
                    className="w-full border rounded px-2 py-1"
                    required
                    type="email"
                    placeholder="<EMAIL>"
                    inputMode="email"
                  />
                  {formErrors.contactEmail && <div className="text-xs text-red-600 mt-1">{formErrors.contactEmail}</div>}
                </div>
                <div className="md:col-span-2">
                  <label className="block font-semibold mb-1">Additional Info</label>
                  <textarea name="additionalInfo" value={form.additionalInfo} onChange={handleFormChange} className="w-full border rounded px-2 py-1" rows={2} />
                </div>
              </form>
            </div>
            <div className="flex-shrink-0 p-6 border-t bg-gray-50">
              <div className="flex justify-end gap-2">
                <button type="button" className="px-4 py-2 bg-gray-200 rounded" onClick={closeModal}>Cancel</button>
                <button type="submit" className="px-4 py-2 bg-blue-600 text-white rounded font-semibold hover:bg-blue-700" onClick={handleSubmit}>{editingClient ? 'Update' : 'Add'} Client</button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Import Modal (UI only for now) */}
      {importing && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-lg relative">
            <button className="absolute top-4 right-4 text-gray-500 hover:text-gray-800 text-2xl" onClick={() => setImporting(false)}>&times;</button>
            <div className="p-6">
              <h3 className="text-xl font-bold mb-4 pr-8">Import Clients from CSV/XLS</h3>
              <p className="mb-4 text-gray-600">Upload a CSV or Excel file with columns matching the client fields. (Import logic coming soon.)</p>
              <input type="file" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" className="mb-4 w-full" />
              <div className="flex justify-end gap-2">
                <button className="px-4 py-2 bg-gray-200 rounded" onClick={() => setImporting(false)}>Cancel</button>
                <button className="px-4 py-2 bg-green-600 text-white rounded font-semibold hover:bg-green-700" disabled>Import</button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ClientManagement;
