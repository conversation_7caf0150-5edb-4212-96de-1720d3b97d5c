/** @license React v16.14.0
 * react-dom-server.browser.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';(function(k,p){"object"===typeof exports&&"undefined"!==typeof module?module.exports=p(require("react")):"function"===typeof define&&define.amd?define(["react"],p):(k=k||self,k.ReactDOMServer=p(k.React))})(this,function(k){function p(a){for(var b="https://reactjs.org/docs/error-decoder.html?invariant="+a,c=1;c<arguments.length;c++)b+="&args[]="+encodeURIComponent(arguments[c]);return"Minified React error #"+a+"; visit "+b+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}
function ta(a){if(-1===a._status){a._status=0;var b=a._ctor;b=b();a._result=b;b.then(function(c){0===a._status&&(c=c.default,a._status=1,a._result=c)},function(c){0===a._status&&(a._status=2,a._result=c)})}}function D(a){if(null==a)return null;if("function"===typeof a)return a.displayName||a.name||null;if("string"===typeof a)return a;switch(a){case L:return"Fragment";case W:return"Portal";case X:return"Profiler";case Y:return"StrictMode";case M:return"Suspense";case Z:return"SuspenseList"}if("object"===
typeof a)switch(a.$$typeof){case aa:return"Context.Consumer";case N:return"Context.Provider";case ba:var b=a.render;b=b.displayName||b.name||"";return a.displayName||(""!==b?"ForwardRef("+b+")":"ForwardRef");case ca:return D(a.type);case ua:return D(a.render);case da:if(a=1===a._status?a._result:null)return D(a)}return null}function E(a,b){for(var c=a._threadCount|0;c<=b;c++)a[c]=a._currentValue2,a._threadCount=c+1}function va(a,b,c,d){if(d&&(d=a.contextType,"object"===typeof d&&null!==d))return E(d,
c),d[c];if(a=a.contextTypes){c={};for(var g in a)c[g]=b[g];b=c}else b=ea;return b}function fa(a){if(ha.call(ia,a))return!0;if(ha.call(ja,a))return!1;if(wa.test(a))return ia[a]=!0;ja[a]=!0;return!1}function xa(a,b,c,d){if(null!==c&&0===c.type)return!1;switch(typeof b){case "function":case "symbol":return!0;case "boolean":if(d)return!1;if(null!==c)return!c.acceptsBooleans;a=a.toLowerCase().slice(0,5);return"data-"!==a&&"aria-"!==a;default:return!1}}function ya(a,b,c,d){if(null===b||"undefined"===typeof b||
xa(a,b,c,d))return!0;if(d)return!1;if(null!==c)switch(c.type){case 3:return!b;case 4:return!1===b;case 5:return isNaN(b);case 6:return isNaN(b)||1>b}return!1}function n(a,b,c,d,g,h){this.acceptsBooleans=2===b||3===b||4===b;this.attributeName=d;this.attributeNamespace=g;this.mustUseProperty=c;this.propertyName=a;this.type=b;this.sanitizeURL=h}function C(a){if("boolean"===typeof a||"number"===typeof a)return""+a;a=""+a;var b=za.exec(a);if(b){var c="",d,g=0;for(d=b.index;d<a.length;d++){switch(a.charCodeAt(d)){case 34:b=
"&quot;";break;case 38:b="&amp;";break;case 39:b="&#x27;";break;case 60:b="&lt;";break;case 62:b="&gt;";break;default:continue}g!==d&&(c+=a.substring(g,d));g=d+1;c+=b}a=g!==d?c+a.substring(g,d):c}return a}function Aa(a,b){var c=m.hasOwnProperty(a)?m[a]:null;var d;if(d="style"!==a)d=null!==c?0===c.type:!(2<a.length)||"o"!==a[0]&&"O"!==a[0]||"n"!==a[1]&&"N"!==a[1]?!1:!0;if(d||ya(a,b,c,!1))return"";if(null!==c){a=c.attributeName;d=c.type;if(3===d||4===d&&!0===b)return a+'=""';c.sanitizeURL&&(b=""+b);
return a+'="'+(C(b)+'"')}return fa(a)?a+'="'+(C(b)+'"'):""}function Ba(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}function A(){if(null===u)throw Error(p(321));return u}function ka(){if(0<I)throw Error(p(312));return{memoizedState:null,queue:null,next:null}}function O(){null===e?null===J?(F=!1,J=e=ka()):(F=!0,e=J):null===e.next?(F=!1,e=e.next=ka()):(F=!0,e=e.next);return e}function la(a,b,c,d){for(;P;)P=!1,I+=1,e=null,c=a(b,d);J=u=null;I=0;e=t=null;return c}function ma(a,b){return"function"===
typeof b?b(a):b}function na(a,b,c){u=A();e=O();if(F){var d=e.queue;b=d.dispatch;if(null!==t&&(c=t.get(d),void 0!==c)){t.delete(d);d=e.memoizedState;do d=a(d,c.action),c=c.next;while(null!==c);e.memoizedState=d;return[d,b]}return[e.memoizedState,b]}a=a===ma?"function"===typeof b?b():b:void 0!==c?c(b):b;e.memoizedState=a;a=e.queue={last:null,dispatch:null};a=a.dispatch=Ca.bind(null,u,a);return[e.memoizedState,a]}function Ca(a,b,c){if(!(25>I))throw Error(p(301));if(a===u)if(P=!0,a={action:c,next:null},
null===t&&(t=new Map),c=t.get(b),void 0===c)t.set(b,a);else{for(b=c;null!==b.next;)b=b.next;b.next=a}}function Q(){}function oa(a){switch(a){case "svg":return"http://www.w3.org/2000/svg";case "math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Da(a){if(void 0===a||null===a)return a;var b="";k.Children.forEach(a,function(a){null!=a&&(b+=a)});return b}function pa(a,b){if(void 0===a)throw Error(p(152,D(b)||"Component"));}function Ea(a,b,c){function d(d,
h){var f=h.prototype&&h.prototype.isReactComponent,g=va(h,b,c,f),v=[],z=!1,e={isMounted:function(a){return!1},enqueueForceUpdate:function(a){if(null===v)return null},enqueueReplaceState:function(a,c){z=!0;v=[c]},enqueueSetState:function(a,c){if(null===v)return null;v.push(c)}};if(f){if(f=new h(d.props,g,e),"function"===typeof h.getDerivedStateFromProps){var l=h.getDerivedStateFromProps.call(null,d.props,f.state);null!=l&&(f.state=x({},f.state,l))}}else if(u={},f=h(d.props,g,e),f=la(h,d.props,f,g),
null==f||null==f.render){a=f;pa(a,h);return}f.props=d.props;f.context=g;f.updater=e;e=f.state;void 0===e&&(f.state=e=null);if("function"===typeof f.UNSAFE_componentWillMount||"function"===typeof f.componentWillMount)if("function"===typeof f.componentWillMount&&"function"!==typeof h.getDerivedStateFromProps&&f.componentWillMount(),"function"===typeof f.UNSAFE_componentWillMount&&"function"!==typeof h.getDerivedStateFromProps&&f.UNSAFE_componentWillMount(),v.length){e=v;var m=z;v=null;z=!1;if(m&&1===
e.length)f.state=e[0];else{l=m?e[0]:f.state;var q=!0;for(m=m?1:0;m<e.length;m++){var r=e[m];r="function"===typeof r?r.call(f,l,d.props,g):r;null!=r&&(q?(q=!1,l=x({},l,r)):x(l,r))}f.state=l}}else v=null;a=f.render();pa(a,h);if("function"===typeof f.getChildContext&&(d=h.childContextTypes,"object"===typeof d)){var k=f.getChildContext();for(var n in k)if(!(n in d))throw Error(p(108,D(h)||"Unknown",n));}k&&(b=x({},b,k))}for(;k.isValidElement(a);){var g=a,h=g.type;if("function"!==typeof h)break;d(g,h)}return{child:a,
context:b}}var x=k.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.assign,l="function"===typeof Symbol&&Symbol.for,W=l?Symbol.for("react.portal"):60106,L=l?Symbol.for("react.fragment"):60107,Y=l?Symbol.for("react.strict_mode"):60108,X=l?Symbol.for("react.profiler"):60114,N=l?Symbol.for("react.provider"):60109,aa=l?Symbol.for("react.context"):60110,Fa=l?Symbol.for("react.concurrent_mode"):60111,ba=l?Symbol.for("react.forward_ref"):60112,M=l?Symbol.for("react.suspense"):60113,Z=l?Symbol.for("react.suspense_list"):
60120,ca=l?Symbol.for("react.memo"):60115,da=l?Symbol.for("react.lazy"):60116,ua=l?Symbol.for("react.block"):60121,Ga=l?Symbol.for("react.fundamental"):60117,Ha=l?Symbol.for("react.scope"):60119;l=k.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;l.hasOwnProperty("ReactCurrentDispatcher")||(l.ReactCurrentDispatcher={current:null});l.hasOwnProperty("ReactCurrentBatchConfig")||(l.ReactCurrentBatchConfig={suspense:null});for(var ea={},q=new Uint16Array(16),K=0;15>K;K++)q[K]=K+1;q[15]=0;var wa=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,
ha=Object.prototype.hasOwnProperty,ja={},ia={},m={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(a){m[a]=new n(a,0,!1,a,null,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(a){var b=a[0];m[b]=new n(b,1,!1,a[1],null,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(a){m[a]=new n(a,
2,!1,a.toLowerCase(),null,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(a){m[a]=new n(a,2,!1,a,null,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(a){m[a]=new n(a,3,!1,a.toLowerCase(),null,!1)});["checked","multiple","muted","selected"].forEach(function(a){m[a]=
new n(a,3,!0,a,null,!1)});["capture","download"].forEach(function(a){m[a]=new n(a,4,!1,a,null,!1)});["cols","rows","size","span"].forEach(function(a){m[a]=new n(a,6,!1,a,null,!1)});["rowSpan","start"].forEach(function(a){m[a]=new n(a,5,!1,a.toLowerCase(),null,!1)});var S=/[\-:]([a-z])/g,T=function(a){return a[1].toUpperCase()};"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(a){var b=
a.replace(S,T);m[b]=new n(b,1,!1,a,null,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(a){var b=a.replace(S,T);m[b]=new n(b,1,!1,a,"http://www.w3.org/1999/xlink",!1)});["xml:base","xml:lang","xml:space"].forEach(function(a){var b=a.replace(S,T);m[b]=new n(b,1,!1,a,"http://www.w3.org/XML/1998/namespace",!1)});["tabIndex","crossOrigin"].forEach(function(a){m[a]=new n(a,1,!1,a.toLowerCase(),null,!1)});m.xlinkHref=new n("xlinkHref",1,!1,"xlink:href",
"http://www.w3.org/1999/xlink",!0);["src","href","action","formAction"].forEach(function(a){m[a]=new n(a,1,!1,a.toLowerCase(),null,!0)});var za=/["'&<>]/,Ia="function"===typeof Object.is?Object.is:Ba,u=null,J=null,e=null,F=!1,P=!1,t=null,I=0,G=0,Ja={readContext:function(a,b){b=G;E(a,b);return a[b]},useContext:function(a,b){A();b=G;E(a,b);return a[b]},useMemo:function(a,b){u=A();e=O();b=void 0===b?null:b;if(null!==e){var c=e.memoizedState;if(null!==c&&null!==b){a:{var d=c[1];if(null===d)d=!1;else{for(var g=
0;g<d.length&&g<b.length;g++)if(!Ia(b[g],d[g])){d=!1;break a}d=!0}}if(d)return c[0]}}a=a();e.memoizedState=[a,b];return a},useReducer:na,useRef:function(a){u=A();e=O();var b=e.memoizedState;return null===b?(a={current:a},e.memoizedState=a):b},useState:function(a){return na(ma,a)},useLayoutEffect:function(a,b){},useCallback:function(a,b){return a},useImperativeHandle:Q,useEffect:Q,useDebugValue:Q,useResponder:function(a,b){return{props:b,responder:a}},useDeferredValue:function(a,b){A();return a},useTransition:function(a){A();
return[function(a){a()},!1]}},qa={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0},Ka=x({menuitem:!0},qa),H={animationIterationCount:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,
gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},La=["Webkit","ms","Moz","O"];Object.keys(H).forEach(function(a){La.forEach(function(b){b=b+a.charAt(0).toUpperCase()+a.substring(1);H[b]=H[a]})});var Ma=/([A-Z])/g,Na=/^ms-/,B=k.Children.toArray,U=l.ReactCurrentDispatcher,
Oa={listing:!0,pre:!0,textarea:!0},Pa=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,ra={},V={},Qa=Object.prototype.hasOwnProperty,Ra={children:null,dangerouslySetInnerHTML:null,suppressContentEditableWarning:null,suppressHydrationWarning:null},sa=function(){function a(a,b){k.isValidElement(a)?a.type!==L?a=[a]:(a=a.props.children,a=k.isValidElement(a)?[a]:B(a)):a=B(a);a={type:null,domNamespace:"http://www.w3.org/1999/xhtml",children:a,childIndex:0,context:ea,footer:""};var c=q[0];if(0===c){var h=q;c=h.length;var d=
2*c;if(!(65536>=d))throw Error(p(304));var z=new Uint16Array(d);z.set(h);q=z;q[0]=c+1;for(h=c;h<d-1;h++)q[h]=h+1;q[d-1]=0}else q[0]=q[c];this.threadID=c;this.stack=[a];this.exhausted=!1;this.currentSelectValue=null;this.previousWasTextNode=!1;this.makeStaticMarkup=b;this.suspenseDepth=0;this.contextIndex=-1;this.contextStack=[];this.contextValueStack=[]}var b=a.prototype;b.destroy=function(){if(!this.exhausted){this.exhausted=!0;this.clearProviders();var a=this.threadID;q[a]=q[0];q[0]=a}};b.pushProvider=
function(a){var b=++this.contextIndex,c=a.type._context,h=this.threadID;E(c,h);var v=c[h];this.contextStack[b]=c;this.contextValueStack[b]=v;c[h]=a.props.value};b.popProvider=function(a){a=this.contextIndex;var b=this.contextStack[a],c=this.contextValueStack[a];this.contextStack[a]=null;this.contextValueStack[a]=null;this.contextIndex--;b[this.threadID]=c};b.clearProviders=function(){for(var a=this.contextIndex;0<=a;a--)this.contextStack[a][this.threadID]=this.contextValueStack[a]};b.read=function(a){if(this.exhausted)return null;
var b=G;G=this.threadID;var c=U.current;U.current=Ja;try{for(var h=[""],v=!1;h[0].length<a;){if(0===this.stack.length){this.exhausted=!0;var z=this.threadID;q[z]=q[0];q[0]=z;break}var f=this.stack[this.stack.length-1];if(v||f.childIndex>=f.children.length){var e=f.footer;""!==e&&(this.previousWasTextNode=!1);this.stack.pop();if("select"===f.type)this.currentSelectValue=null;else if(null!=f.type&&null!=f.type.type&&f.type.type.$$typeof===N)this.popProvider(f.type);else if(f.type===M){this.suspenseDepth--;
var l=h.pop();if(v){v=!1;var m=f.fallbackFrame;if(!m)throw Error(p(303));this.stack.push(m);h[this.suspenseDepth]+="\x3c!--$!--\x3e";continue}else h[this.suspenseDepth]+=l}h[this.suspenseDepth]+=e}else{var k=f.children[f.childIndex++],n="";try{n+=this.render(k,f.context,f.domNamespace)}catch(R){if(null!=R&&"function"===typeof R.then)throw Error(p(294));throw R;}finally{}h.length<=this.suspenseDepth&&h.push("");h[this.suspenseDepth]+=n}}return h[0]}finally{U.current=c,G=b}};b.render=function(a,b,g){if("string"===
typeof a||"number"===typeof a){g=""+a;if(""===g)return"";if(this.makeStaticMarkup)return C(g);if(this.previousWasTextNode)return"\x3c!-- --\x3e"+C(g);this.previousWasTextNode=!0;return C(g)}b=Ea(a,b,this.threadID);a=b.child;b=b.context;if(null===a||!1===a)return"";if(!k.isValidElement(a)){if(null!=a&&null!=a.$$typeof){g=a.$$typeof;if(g===W)throw Error(p(257));throw Error(p(258,g.toString()));}a=B(a);this.stack.push({type:null,domNamespace:g,children:a,childIndex:0,context:b,footer:""});return""}var c=
a.type;if("string"===typeof c)return this.renderDOM(a,b,g);switch(c){case Y:case Fa:case X:case Z:case L:return a=B(a.props.children),this.stack.push({type:null,domNamespace:g,children:a,childIndex:0,context:b,footer:""}),"";case M:throw Error(p(294));}if("object"===typeof c&&null!==c)switch(c.$$typeof){case ba:u={};var d=c.render(a.props,a.ref);d=la(c.render,a.props,d,a.ref);d=B(d);this.stack.push({type:null,domNamespace:g,children:d,childIndex:0,context:b,footer:""});return"";case ca:return a=[k.createElement(c.type,
x({ref:a.ref},a.props))],this.stack.push({type:null,domNamespace:g,children:a,childIndex:0,context:b,footer:""}),"";case N:return c=B(a.props.children),g={type:a,domNamespace:g,children:c,childIndex:0,context:b,footer:""},this.pushProvider(a),this.stack.push(g),"";case aa:c=a.type;d=a.props;var e=this.threadID;E(c,e);c=B(d.children(c[e]));this.stack.push({type:a,domNamespace:g,children:c,childIndex:0,context:b,footer:""});return"";case Ga:throw Error(p(338));case da:switch(c=a.type,ta(c),c._status){case 1:return a=
[k.createElement(c._result,x({ref:a.ref},a.props))],this.stack.push({type:null,domNamespace:g,children:a,childIndex:0,context:b,footer:""}),"";case 2:throw c._result;default:throw Error(p(295));}case Ha:throw Error(p(343));}throw Error(p(130,null==c?c:typeof c,""));};b.renderDOM=function(a,b,g){var c=a.type.toLowerCase();"http://www.w3.org/1999/xhtml"===g&&oa(c);if(!ra.hasOwnProperty(c)){if(!Pa.test(c))throw Error(p(65,c));ra[c]=!0}var d=a.props;if("input"===c)d=x({type:void 0},d,{defaultChecked:void 0,
defaultValue:void 0,value:null!=d.value?d.value:d.defaultValue,checked:null!=d.checked?d.checked:d.defaultChecked});else if("textarea"===c){var e=d.value;if(null==e){e=d.defaultValue;var f=d.children;if(null!=f){if(null!=e)throw Error(p(92));if(Array.isArray(f)){if(!(1>=f.length))throw Error(p(93));f=f[0]}e=""+f}null==e&&(e="")}d=x({},d,{value:void 0,children:""+e})}else if("select"===c)this.currentSelectValue=null!=d.value?d.value:d.defaultValue,d=x({},d,{value:void 0});else if("option"===c){f=this.currentSelectValue;
var l=Da(d.children);if(null!=f){var m=null!=d.value?d.value+"":l;e=!1;if(Array.isArray(f))for(var k=0;k<f.length;k++){if(""+f[k]===m){e=!0;break}}else e=""+f===m;d=x({selected:void 0,children:void 0},d,{selected:e,children:l})}}if(e=d){if(Ka[c]&&(null!=e.children||null!=e.dangerouslySetInnerHTML))throw Error(p(137,c,""));if(null!=e.dangerouslySetInnerHTML){if(null!=e.children)throw Error(p(60));if(!("object"===typeof e.dangerouslySetInnerHTML&&"__html"in e.dangerouslySetInnerHTML))throw Error(p(61));
}if(null!=e.style&&"object"!==typeof e.style)throw Error(p(62,""));}e=d;f=this.makeStaticMarkup;l=1===this.stack.length;m="<"+a.type;for(w in e)if(Qa.call(e,w)){var n=e[w];if(null!=n){if("style"===w){k=void 0;var q="",u="";for(k in n)if(n.hasOwnProperty(k)){var y=0===k.indexOf("--"),r=n[k];if(null!=r){if(y)var t=k;else if(t=k,V.hasOwnProperty(t))t=V[t];else{var A=t.replace(Ma,"-$1").toLowerCase().replace(Na,"-ms-");t=V[t]=A}q+=u+t+":";u=k;y=null==r||"boolean"===typeof r||""===r?"":y||"number"!==typeof r||
0===r||H.hasOwnProperty(u)&&H[u]?(""+r).trim():r+"px";q+=y;u=";"}}n=q||null}k=null;b:if(y=c,r=e,-1===y.indexOf("-"))y="string"===typeof r.is;else switch(y){case "annotation-xml":case "color-profile":case "font-face":case "font-face-src":case "font-face-uri":case "font-face-format":case "font-face-name":case "missing-glyph":y=!1;break b;default:y=!0}y?Ra.hasOwnProperty(w)||(k=w,k=fa(k)&&null!=n?k+'="'+(C(n)+'"'):""):k=Aa(w,n);k&&(m+=" "+k)}}f||l&&(m+=' data-reactroot=""');var w=m;e="";qa.hasOwnProperty(c)?
w+="/>":(w+=">",e="</"+a.type+">");a:{f=d.dangerouslySetInnerHTML;if(null!=f){if(null!=f.__html){f=f.__html;break a}}else if(f=d.children,"string"===typeof f||"number"===typeof f){f=C(f);break a}f=null}null!=f?(d=[],Oa.hasOwnProperty(c)&&"\n"===f.charAt(0)&&(w+="\n"),w+=f):d=B(d.children);a=a.type;g=null==g||"http://www.w3.org/1999/xhtml"===g?oa(a):"http://www.w3.org/2000/svg"===g&&"foreignObject"===a?"http://www.w3.org/1999/xhtml":g;this.stack.push({domNamespace:g,type:c,children:d,childIndex:0,
context:b,footer:e});this.previousWasTextNode=!1;return w};return a}();l={renderToString:function(a){a=new sa(a,!1);try{return a.read(Infinity)}finally{a.destroy()}},renderToStaticMarkup:function(a){a=new sa(a,!0);try{return a.read(Infinity)}finally{a.destroy()}},renderToNodeStream:function(){throw Error(p(207));},renderToStaticNodeStream:function(){throw Error(p(208));},version:"16.14.0"};return l.default||l});
